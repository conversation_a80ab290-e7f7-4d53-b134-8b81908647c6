import { Box } from '@mui/material'
import React, { useEffect, useState, useRef } from 'react'
import { useHostStore } from '@store/hostStore'
import RoarSplash from '@components/Header/RoarSplash'

interface IFrameProps {
  url: string
}

const IFrame: React.FC<IFrameProps> = ({ url }) => {
  const { loadingCount, setAppTransitioning } = useHostStore((state) => state)
  const [isIframeLoading, setIsIframeLoading] = useState(true)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Simple iframe load handler
  const handleIframeLoad = () => {
    console.log('Iframe onLoad event fired for URL:', url)

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }

    // Set a timeout to hide the splash after iframe loads
    // This gives the iframe content time to fully render
    timeoutRef.current = setTimeout(() => {
      console.log('Hiding splash screen after timeout')
      setIsIframeLoading(false)
      setAppTransitioning(false)
    }, 1500) // 1.5 seconds after iframe onLoad
  }

  // Reset loading state when URL changes
  useEffect(() => {
    console.log('URL changed to:', url)
    setIsIframeLoading(true)
    setAppTransitioning(true)

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }

    // Set a maximum timeout as absolute fallback
    // This ensures splash never stays forever
    timeoutRef.current = setTimeout(() => {
      console.log('Maximum timeout reached, forcing splash to hide')
      setIsIframeLoading(false)
      setAppTransitioning(false)
    }, 5000) // 5 seconds maximum
  }, [url, setAppTransitioning])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  // Show RoarSplash if either host is loading or iframe is loading
  const shouldShowLoading = loadingCount > 0 || isIframeLoading

  console.log('Loading states:', { loadingCount, isIframeLoading, shouldShowLoading, url })

  return shouldShowLoading ? (
    <Box>
      <RoarSplash />
      {/* Debug button - remove in production */}
      <Box
        sx={{
          position: 'fixed',
          top: 100,
          right: 20,
          zIndex: 9999,
          backgroundColor: 'red',
          color: 'white',
          padding: '10px',
          cursor: 'pointer',
          borderRadius: '5px'
        }}
        onClick={() => {
          console.log('Debug: Force hiding splash')
          setIsIframeLoading(false)
          setAppTransitioning(false)
        }}
      >
        DEBUG: Hide Splash
      </Box>
    </Box>
  ) : (
    <Box>
      <iframe
        ref={iframeRef}
        src={url}
        title="Marcel"
        style={{ height: 'calc(100vh - 64px)', width: '100%', border: 'none' }}
        onLoad={handleIframeLoad}
      />
    </Box>
  )
}

export default IFrame
