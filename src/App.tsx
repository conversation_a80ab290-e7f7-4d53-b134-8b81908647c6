import { Route, Routes, useLocation } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import { Container, CssBaseline } from '@mui/material'
import theme from './styles/theme'
import { AuthenticatedTemplate, UnauthenticatedTemplate, useIsAuthenticated } from '@azure/msal-react'
import { RoutePaths } from './types/routes'
import MSALAuth from './auth/MSALAuth'
// import './utils/dayjsConfig'
import HomeDashboard from './components/Dashboard'
import { useEffect } from 'react'
import MarcelHeader from '@components/Header'
import TimesheetApp from '@pages/apps/Timesheet'
import TimesheetApprovalsApp from '@pages/apps/TimesheetApprovals'
// import OnBehalfOf from '@pages/OnBehalfOf'
// import Substitutes from '@pages/Substitutes'
// import Forwards from '@pages/Forwards'
// import MissingTimesheetDashboard from '@pages/TimesheetDashboard'
// import TimesheetReport from '@pages/TimesheetReport'
// import MissingTimesheetCalendar from '@pages/MissingTimesheetCalendar'
// import MissingTimesheetReport from '@pages/MissingTimesheetReport'
// import WarningSummaryPage from '@pages/TimesheetDashboard/WarningSummaryPage'
import { useHostStore } from '@store/hostStore'
import PermissionsApp from '@pages/apps/Permissions'
// import MyTimesheetReport from '@pages/MyTimesheetReport'
// import PrivacyNotice from '@pages/PrivacyNotice'
// import Header from '@pages/Marcel'
// import TimesheetApp from '@pages/Marcel/apps/Timesheet'
// import TimesheetApprovalsApp from '@pages/Marcel/apps/TimesheetApprovals'

function App() {
  // print current route path
  const location = useLocation()
  const isAuthenticated = useIsAuthenticated()
  const { setAppTransitioning } = useHostStore((state) => state)

  useEffect(() => {
    console.log(' in Current path:', location.pathname, location)

    // Trigger app transition loading when navigating to iframe-based apps
    const iframeRoutes = ['/' + RoutePaths.TIMESHEET, '/' + RoutePaths.APPROVALS, '/' + RoutePaths.PERMISSIONS]

    if (iframeRoutes.includes(location.pathname)) {
      console.log('Detected iframe route, setting app transitioning to true')
      setAppTransitioning(true)
    } else {
      // Clear transitioning state when navigating to non-iframe routes (like dashboard)
      setAppTransitioning(false)
    }
  }, [location, setAppTransitioning])

  useEffect(() => {
    console.log('auth OUT')
    if (isAuthenticated) {
      console.log('auth in')
      useHostStore.getState().getUserDetail()
    }
  }, [isAuthenticated])

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthenticatedTemplate>
        <MarcelHeader />
        <Container maxWidth={false} sx={{ maxWidth: '2560px' }} disableGutters>
          <Routes>
            <Route path={RoutePaths.DASHBOARD} element={<HomeDashboard />} />
            <Route path={RoutePaths.TIMESHEET} element={<TimesheetApp />} />
            <Route path={RoutePaths.APPROVALS} element={<TimesheetApprovalsApp />} />
            <Route path={RoutePaths.PERMISSIONS} element={<PermissionsApp />} />
          </Routes>
        </Container>
      </AuthenticatedTemplate>
      <UnauthenticatedTemplate>
        <MSALAuth />
      </UnauthenticatedTemplate>
    </ThemeProvider>
  )
}

export default App
